#!/usr/bin/env python3
"""Unit tests for the events pipeline functions."""

import os
import tempfile
from datetime import datetime, timedelta

import duckdb
import pytest

from events_pipeline import (
    check_source_table,
    copy_new_events,
    extract_events_incremental,
    get_last_processed_timestamp,
    setup_staging_table,
)


@pytest.fixture
def temp_source_db():
    """Create a temporary source database with test data."""
    # Create a temporary file path but don't create the file
    import tempfile
    fd, db_file = tempfile.mkstemp(suffix=".duckdb")
    os.close(fd)  # Close the file descriptor
    os.unlink(db_file)  # Remove the empty file so DuckD<PERSON> can create it

    conn = duckdb.connect(db_file)
    
    # Create the schema and table
    conn.execute("CREATE SCHEMA IF NOT EXISTS ducklake_src")
    conn.execute("""
        CREATE TABLE IF NOT EXISTS ducklake_src.events (
            event_id VARCHAR,
            event_type VARCHAR,
            event_timestamp TIMESTAMP,
            details JSO<PERSON>,
            created_at TIMESTAMP
        )
    """)
    
    # Insert some test data
    base_time = datetime.now() - timedelta(days=1)
    test_events = []
    
    for i in range(5):
        event_time = base_time + timedelta(hours=i)
        test_events.append((
            f"event_{i:03d}",
            "test_event",
            event_time,
            f'{{"user_id": {i}, "action": "test_action_{i}"}}',
            event_time
        ))
    
    conn.executemany("""
        INSERT INTO ducklake_src.events 
        (event_id, event_type, event_timestamp, details, created_at)
        VALUES (?, ?, ?, ?, ?)
    """, test_events)
    
    conn.close()
    
    yield db_file
    
    # Cleanup
    os.unlink(db_file)


@pytest.fixture
def temp_staging_db():
    """Create a temporary staging database."""
    fd, db_file = tempfile.mkstemp(suffix=".duckdb")
    os.close(fd)  # Close the file descriptor
    os.unlink(db_file)  # Remove the empty file so DuckDB can create it

    yield db_file

    # Cleanup
    if os.path.exists(db_file):
        os.unlink(db_file)


@pytest.fixture
def empty_source_db():
    """Create a temporary source database without the events table."""
    fd, db_file = tempfile.mkstemp(suffix=".duckdb")
    os.close(fd)  # Close the file descriptor
    os.unlink(db_file)  # Remove the empty file so DuckDB can create it

    # Just create an empty database
    conn = duckdb.connect(db_file)
    conn.close()

    yield db_file

    # Cleanup
    os.unlink(db_file)


class TestCheckSourceTable:
    """Test the check_source_table function."""
    
    def test_existing_table(self, temp_source_db):
        """Test checking an existing table."""
        result = check_source_table(temp_source_db)
        
        assert result["exists"] is True
        assert result["total_rows"] == 5
        assert result["min_ts"] is not None
        assert result["max_ts"] is not None
    
    def test_nonexistent_table(self, empty_source_db):
        """Test checking a non-existent table."""
        result = check_source_table(empty_source_db)
        
        assert result["exists"] is False
        assert result["total_rows"] == 0
        assert result["min_ts"] is None
        assert result["max_ts"] is None


class TestSetupStagingTable:
    """Test the setup_staging_table function."""
    
    def test_create_staging_table(self, temp_staging_db):
        """Test creating a staging table."""
        conn = duckdb.connect(temp_staging_db)
        
        # Should not raise an exception
        setup_staging_table(conn, "test_staging")
        
        # Check that schema and table were created
        schemas = conn.execute(
            "SELECT schema_name FROM information_schema.schemata WHERE schema_name = 'test_staging'"
        ).fetchall()
        assert len(schemas) == 1
        
        tables = conn.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'test_staging' AND table_name = 'events'
        """).fetchall()
        assert len(tables) == 1
        
        conn.close()


class TestGetLastProcessedTimestamp:
    """Test the get_last_processed_timestamp function."""
    
    def test_empty_staging_table(self, temp_staging_db):
        """Test getting timestamp from empty staging table."""
        conn = duckdb.connect(temp_staging_db)
        setup_staging_table(conn, "test_staging")
        
        result = get_last_processed_timestamp(conn, "test_staging")
        assert result is None
        
        conn.close()
    
    def test_staging_table_with_data(self, temp_staging_db):
        """Test getting timestamp from staging table with data."""
        conn = duckdb.connect(temp_staging_db)
        setup_staging_table(conn, "test_staging")
        
        # Insert some test data
        test_time = datetime.now()
        conn.execute("""
            INSERT INTO test_staging.events 
            (event_id, event_type, event_timestamp, details, created_at)
            VALUES (?, ?, ?, ?, ?)
        """, ("test_001", "test", test_time, '{}', test_time))
        
        result = get_last_processed_timestamp(conn, "test_staging")
        assert result is not None
        # Note: DuckDB might return a slightly different timestamp format
        
        conn.close()


class TestCopyNewEvents:
    """Test the copy_new_events function."""
    
    def test_copy_all_events_first_run(self, temp_source_db, temp_staging_db):
        """Test copying all events on first run."""
        staging_conn = duckdb.connect(temp_staging_db)
        
        # Attach source database
        staging_conn.execute(f"ATTACH '{temp_source_db}' AS source_db")
        
        # Setup staging table
        setup_staging_table(staging_conn, "test_staging")
        
        # Copy events (first run, no last_timestamp)
        count = copy_new_events(staging_conn, "test_staging", None)
        
        assert count == 5
        
        # Verify data was copied
        result = staging_conn.execute("SELECT COUNT(*) FROM test_staging.events").fetchone()
        assert result[0] == 5
        
        staging_conn.close()


class TestExtractEventsIncremental:
    """Test the main extract_events_incremental function."""
    
    def test_extract_from_existing_source(self, temp_source_db, temp_staging_db):
        """Test extracting from an existing source."""
        result = extract_events_incremental(
            src_db_file=temp_source_db,
            staging_db_file=temp_staging_db,
            dataset_name="test_staging"
        )
        
        assert result["rows_loaded"] == 5
        assert result["duration"] >= 0
        assert "started_at" in result
        assert "finished_at" in result
    
    def test_extract_from_nonexistent_source(self, empty_source_db, temp_staging_db):
        """Test extracting from a source without events table."""
        result = extract_events_incremental(
            src_db_file=empty_source_db,
            staging_db_file=temp_staging_db,
            dataset_name="test_staging"
        )
        
        assert result["rows_loaded"] == 0
        assert result["duration"] >= 0
    
    def test_incremental_extraction(self, temp_source_db, temp_staging_db):
        """Test that incremental extraction works correctly."""
        # First extraction
        result1 = extract_events_incremental(
            src_db_file=temp_source_db,
            staging_db_file=temp_staging_db,
            dataset_name="test_staging"
        )
        assert result1["rows_loaded"] == 5
        
        # Second extraction (should find no new events)
        result2 = extract_events_incremental(
            src_db_file=temp_source_db,
            staging_db_file=temp_staging_db,
            dataset_name="test_staging"
        )
        assert result2["rows_loaded"] == 0
        
        # Add new data to source
        src_conn = duckdb.connect(temp_source_db)
        new_time = datetime.now()
        src_conn.execute("""
            INSERT INTO ducklake_src.events 
            (event_id, event_type, event_timestamp, details, created_at)
            VALUES (?, ?, ?, ?, ?)
        """, ("event_new", "new_event", new_time, '{"new": true}', new_time))
        src_conn.close()
        
        # Third extraction (should find 1 new event)
        result3 = extract_events_incremental(
            src_db_file=temp_source_db,
            staging_db_file=temp_staging_db,
            dataset_name="test_staging"
        )
        assert result3["rows_loaded"] == 1


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
