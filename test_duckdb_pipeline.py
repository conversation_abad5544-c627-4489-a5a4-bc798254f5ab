#!/usr/bin/env python3
"""Test script for the DuckDB-to-DuckDB events pipeline."""

import os
import tempfile
from datetime import datetime, timedelta

import duckdb


def create_test_source_data(db_file: str) -> None:
    """Create a test source database with sample events."""
    conn = duckdb.connect(db_file)
    
    # Create the schema and table
    conn.execute("CREATE SCHEMA IF NOT EXISTS ducklake_src")
    conn.execute("""
        CREATE TABLE IF NOT EXISTS ducklake_src.events (
            event_id VARCHAR,
            event_type VARCHAR,
            event_timestamp TIMESTAMP,
            details JSON,
            created_at TIMESTAMP
        )
    """)
    
    # Insert some test data
    base_time = datetime.now() - timedelta(days=1)
    test_events = []
    
    for i in range(10):
        event_time = base_time + timedelta(hours=i)
        test_events.append((
            f"event_{i:03d}",
            "test_event",
            event_time,
            f'{{"user_id": {i}, "action": "test_action_{i}"}}',
            event_time
        ))
    
    conn.executemany("""
        INSERT INTO ducklake_src.events 
        (event_id, event_type, event_timestamp, details, created_at)
        VALUES (?, ?, ?, ?, ?)
    """, test_events)
    
    conn.close()
    print(f"Created test source database with {len(test_events)} events")

def create_test_catalog(catalog_file: str, data_path: str) -> None:
    """Create a test Duck Lake catalog."""
    # Ensure data directory exists
    os.makedirs(data_path, exist_ok=True)
    
    # Create a simple catalog database
    conn = duckdb.connect(catalog_file)
    conn.execute("FORCE INSTALL ducklake FROM core_nightly; LOAD ducklake;")
    
    # Initialize Duck Lake catalog
    conn.execute("CREATE SCHEMA IF NOT EXISTS main")
    
    conn.close()
    print(f"Created test catalog database: {catalog_file}")

def test_pipeline():
    """Test the complete DuckDB-to-DuckDB pipeline."""
    # Create temporary directory for test files
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"Testing in temporary directory: {temp_dir}")
        
        # File paths
        source_db = os.path.join(temp_dir, "source.duckdb")
        catalog_db = os.path.join(temp_dir, "catalog.duckdb")
        staging_db = os.path.join(temp_dir, "staging.duckdb")
        data_path = os.path.join(temp_dir, "ducklake-data")
        
        # Step 1: Create test data
        print("\n1. Creating test source data...")
        create_test_source_data(source_db)
        
        # Step 2: Create test catalog
        print("\n2. Creating test catalog...")
        create_test_catalog(catalog_db, data_path)
        
        # Step 3: Test extraction
        print("\n3. Testing extraction...")
        from events_pipeline import extract_events_incremental
        
        load_info = extract_events_incremental(
            src_db_file=source_db,
            staging_db_file=staging_db,
            dataset_name="events_staging"
        )
        
        print(f"Extraction result: {load_info}")
        assert load_info["rows_loaded"] == 10, f"Expected 10 rows, got {load_info['rows_loaded']}"
        
        # Step 4: Test promotion
        print("\n4. Testing promotion...")
        from events_pipeline import promote_events_to_lake
        
        promoted_count = promote_events_to_lake(
            staging_db_file=staging_db,
            catalog_conn=catalog_db,
            data_path=data_path,
            dataset_name="events_staging"
        )
        
        print(f"Promotion result: {promoted_count} events promoted")
        assert promoted_count == 10, f"Expected 10 events promoted, got {promoted_count}"
        
        # Step 5: Test incremental extraction (should find no new events)
        print("\n5. Testing incremental extraction...")
        load_info2 = extract_events_incremental(
            src_db_file=source_db,
            staging_db_file=staging_db,
            dataset_name="events_staging"
        )
        
        print(f"Second extraction result: {load_info2}")
        assert load_info2["rows_loaded"] == 0, f"Expected 0 new rows, got {load_info2['rows_loaded']}"
        
        # Step 6: Add more data and test incremental
        print("\n6. Adding more data and testing incremental...")
        conn = duckdb.connect(source_db)
        new_time = datetime.now()
        conn.execute("""
            INSERT INTO ducklake_src.events 
            (event_id, event_type, event_timestamp, details, created_at)
            VALUES (?, ?, ?, ?, ?)
        """, ("event_new", "new_event", new_time, '{"new": true}', new_time))
        conn.close()
        
        load_info3 = extract_events_incremental(
            src_db_file=source_db,
            staging_db_file=staging_db,
            dataset_name="events_staging"
        )
        
        print(f"Third extraction result: {load_info3}")
        assert load_info3["rows_loaded"] == 1, f"Expected 1 new row, got {load_info3['rows_loaded']}"
        
        print("\n✅ All tests passed!")

if __name__ == "__main__":
    test_pipeline()
