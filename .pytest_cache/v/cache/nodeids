["test_events_pipeline.py::TestCheckSourceTable::test_existing_table", "test_events_pipeline.py::TestCheckSourceTable::test_nonexistent_table", "test_events_pipeline.py::TestCopyNewEvents::test_copy_all_events_first_run", "test_events_pipeline.py::TestExtractEventsIncremental::test_extract_from_existing_source", "test_events_pipeline.py::TestExtractEventsIncremental::test_extract_from_nonexistent_source", "test_events_pipeline.py::TestExtractEventsIncremental::test_incremental_extraction", "test_events_pipeline.py::TestGetLastProcessedTimestamp::test_empty_staging_table", "test_events_pipeline.py::TestGetLastProcessedTimestamp::test_staging_table_with_data", "test_events_pipeline.py::TestSetupStagingTable::test_create_staging_table", "tests/test_unit.py::TestCheckSourceTable::test_existing_table", "tests/test_unit.py::TestCheckSourceTable::test_nonexistent_table", "tests/test_unit.py::TestCopyNewEvents::test_copy_all_events_first_run", "tests/test_unit.py::TestExtractEventsIncremental::test_extract_from_existing_source", "tests/test_unit.py::TestExtractEventsIncremental::test_extract_from_nonexistent_source", "tests/test_unit.py::TestExtractEventsIncremental::test_incremental_extraction", "tests/test_unit.py::TestGetLastProcessedTimestamp::test_empty_staging_table", "tests/test_unit.py::TestGetLastProcessedTimestamp::test_staging_table_with_data", "tests/test_unit.py::TestSetupStagingTable::test_create_staging_table"]